import React from 'react';
import { Star, User, Calendar as Calendar<PERSON><PERSON>, Diamond } from 'lucide-react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { Task } from '../../types';

interface TaskCardProps {
    task: Task;
    onEditTask: (task: Task) => void;
}

export const TaskCard: React.FC<TaskCardProps> = ({ task, onEditTask }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: task.id });
    
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        boxShadow: isDragging ? '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1)' : 'none'
    };
    
    const priorityStyles: Record<string, string> = {
        High: 'bg-red-500/20 text-red-400 border-red-500/30',
        Medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        Low: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
        None: 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    };
    
    return (
        <div 
            ref={setNodeRef} 
            style={style} 
            {...attributes} 
            {...listeners} 
            onClick={() => onEditTask(task)} 
            className="bg-gray-800 p-3 rounded-lg border border-gray-700/50 hover:border-indigo-500/50 cursor-pointer transition-all duration-200 ease-in-out transform hover:-translate-y-0.5"
        >
            <div className="flex justify-between">
                <p className="text-base font-medium text-gray-200 pr-4">{task.title}</p>
                {task.isMilestone && <Star size={16} className="text-amber-400" />}
            </div>
            <div className="flex items-center justify-between mt-3 text-sm">
                <div className="flex items-center space-x-2">
                    <span className={`px-2 py-0.5 text-xs font-semibold rounded-full border ${priorityStyles[task.priority || 'None'] || ''}`}>
                        {task.priority || 'None'}
                    </span>
                </div>
                <div className="flex items-center space-x-2">
                    {task.deadline &&
                        <div className="flex items-center space-x-1 text-red-400" title={`Deadline: ${new Date(task.deadline).toLocaleDateString()}`}>
                            <Diamond size={14}/>
                        </div>
                    }
                    {task.dueDate &&
                        <div className="flex items-center space-x-1 text-gray-400">
                            <CalendarIcon size={14} />
                            <span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                        </div>
                    }
                    <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center" title="Assignee">
                        <User size={14} className="text-gray-300" />
                    </div>
                </div>
            </div>
        </div>
    );
};
