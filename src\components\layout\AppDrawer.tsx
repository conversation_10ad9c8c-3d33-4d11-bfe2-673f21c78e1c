import React from 'react';
import { Plus, User, LayoutDashboard, Folder, Sun } from 'lucide-react';
import type { AppDrawerProps } from '../../types';

export const AppDrawer: React.FC<AppDrawerProps> = ({ 
    isOpen, 
    projects, 
    activeProject, 
    setActiveProject, 
    onNewProject, 
    currentView, 
    setCurrentView, 
    onLogout, 
    user 
}) => (
    <div className={`bg-gray-800 text-white flex flex-col transition-all duration-300 ${isOpen ? 'w-64' : 'w-0'} overflow-hidden`}>
        <div className="p-4 border-b border-gray-700 flex-shrink-0">
            <h2 className="text-xl font-bold">Momentum</h2>
        </div>
        <nav className="flex-1 p-2 space-y-1 overflow-y-auto">
            <button 
                onClick={() => { setActiveProject(null); setCurrentView('Dashboard');}} 
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${currentView === 'Dashboard' ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}
            >
                <LayoutDashboard size={18} /> Dashboard
            </button>
            <button 
                onClick={() => { setActiveProject(null); setCurrentView('Day');}} 
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${currentView === 'Day' ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}
            >
                <Sun size={18} /> Day Planner
            </button>
            <div className="pt-2 mt-2 border-t border-gray-700">
                <h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Projects</h3>
            </div>
            {projects.map(p => (
                <div key={p.id}>
                    <button 
                        onClick={() => { setActiveProject(p); if(['Dashboard', 'Day'].includes(currentView)) setCurrentView('Board'); }} 
                        className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${activeProject?.id === p.id ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}
                    >
                        <Folder size={18} /> {p.name}
                    </button>
                    {activeProject?.id === p.id && (
                        <div className="pl-8 mt-1 space-y-1">
                            <button 
                                onClick={() => setCurrentView('Board')} 
                                className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Board' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
                            >
                                Board
                            </button>
                            <button 
                                onClick={() => setCurrentView('List')} 
                                className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'List' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
                            >
                                List
                            </button>
                            <button 
                                onClick={() => setCurrentView('Calendar')} 
                                className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Calendar' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
                            >
                                Calendar
                            </button>
                            <button 
                                onClick={() => setCurrentView('Gantt')} 
                                className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Gantt' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
                            >
                                Gantt
                            </button>
                        </div>
                    )}
                </div>
            ))}
        </nav>
        <div className="p-2 border-t border-gray-700 flex-shrink-0">
            <button 
                onClick={onNewProject} 
                className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700"
            >
                <Plus size={18} /> New Project
            </button>
        </div>
        <div className="p-2 border-t border-gray-700 flex-shrink-0">
            <div className="flex items-center gap-2 p-2">
                <User className="w-8 h-8 bg-indigo-600 rounded-full p-1.5"/>
                <div>
                    <p className="font-semibold text-sm">{user?.name}</p>
                    <p className="text-xs text-gray-400 font-mono">{user?.publicKey.substring(0, 16)}...</p>
                </div>
            </div>
        </div>
    </div>
);
