import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import hooks
import { useAuth } from './hooks';

// Import components
import { LoginPage, CreateIdentityPage } from './components/auth';
import { ProtectedRoute } from './components/routing';
import { MainApp } from './components/MainApp';
import { LoadingSpinner } from './components/ui';

// --- Main App Component ---
export default function App() {
    // Authentication
    const { user, setUser, isAuthReady, handleLogout } = useAuth();

    // Render Logic
    if (!isAuthReady) return <LoadingSpinner fullScreen={true} />;

    const handleLogoutAndRedirect = () => {
        if (handleLogout()) {
            return true;
        }
        return false;
    };

    return (
        <Router>
            <Routes>
                {/* Authentication Routes */}
                <Route
                    path="/login"
                    element={
                        user ? (
                            <Navigate to="/" replace />
                        ) : (
                            <LoginPage onAuthenticated={setUser} />
                        )
                    }
                />
                <Route
                    path="/create-identity"
                    element={
                        user ? (
                            <Navigate to="/" replace />
                        ) : (
                            <CreateIdentityPage onIdentityCreated={setUser} />
                        )
                    }
                />

                {/* Protected Main App Route */}
                <Route
                    path="/*"
                    element={
                        <ProtectedRoute user={user} isAuthReady={isAuthReady}>
                            <MainApp user={user!} onLogout={handleLogoutAndRedirect} />
                        </ProtectedRoute>
                    }
                />
            </Routes>
        </Router>
    );
}