import React from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';
import type { DayPlannerViewProps } from '../../types';

export const DayPlannerView: React.FC<DayPlannerViewProps> = ({ tasks, date, setDate, onEditTask }) => {
    const changeDay = (offset: number) => {
        setDate(prev => {
            const newDate = new Date(prev);
            newDate.setDate(newDate.getDate() + offset);
            return newDate;
        });
    };
    
    return (
        <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50">
            <header className="flex items-center justify-between p-4 border-b border-gray-700/50">
                <div className="flex items-center gap-4">
                    <button onClick={() => changeDay(-1)} className="p-2 rounded-md hover:bg-gray-700">
                        <ChevronLeft/>
                    </button>
                    <input
                        type="date"
                        value={date.toISOString().split('T')[0]}
                        onChange={e => setDate(new Date(e.target.value))}
                        className="bg-gray-700 rounded-md p-2 text-white"
                    />
                    <button onClick={() => changeDay(1)} className="p-2 rounded-md hover:bg-gray-700">
                        <ChevronRight/>
                    </button>
                </div>
                <button onClick={() => setDate(new Date())} className="px-4 py-2 rounded-md border border-gray-600 hover:bg-gray-700 text-sm">
                    Today
                </button>
            </header>
            <div className="p-4">
                {tasks.length > 0 ? (
                    <div className="space-y-3">
                        {tasks.map(task => (
                            <div key={task.id} onClick={() => onEditTask(task)} className="bg-gray-800 p-3 rounded-lg border border-gray-700/50 hover:border-indigo-500/50 cursor-pointer flex items-center justify-between">
                                <div>
                                    <p className="text-base font-medium text-gray-200">{task.title}</p>
                                    <p className="text-xs text-gray-400 mt-1">{task.projectName}</p>
                                </div>
                                <div className="flex items-center gap-4">
                                    {task.isMilestone && <Star size={16} className="text-amber-400" />}
                                    {task.dueDate && <span className="text-sm text-gray-300">{new Date(task.dueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-10 text-gray-400">No tasks scheduled for this day.</div>
                )}
            </div>
        </div>
    );
};
