import React from 'react';
import { Navigate } from 'react-router-dom';
import type { User } from '../../types';

interface ProtectedRouteProps {
    children: React.ReactNode;
    user: User | null;
    isAuthReady: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
    children, 
    user, 
    isAuthReady 
}) => {
    // Show loading while auth is being checked
    if (!isAuthReady) {
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
            </div>
        );
    }

    // Redirect to login if not authenticated
    if (!user) {
        return <Navigate to="/login" replace />;
    }

    // Render the protected content
    return <>{children}</>;
};
