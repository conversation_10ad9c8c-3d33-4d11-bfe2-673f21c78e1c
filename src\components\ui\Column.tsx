import React from 'react';
import { Plus } from 'lucide-react';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import type { Task } from '../../types';
import { TaskCard } from './TaskCard';

interface ColumnProps {
    id: string;
    title: string;
    tasks: Task[];
    onEditTask: (task: Task) => void;
    onAddTask: () => void;
}

export const Column: React.FC<ColumnProps> = ({ id, title, tasks, onEditTask, onAddTask }) => {
    const { setNodeRef, isOver } = useSortable({ id: id, data: { type: 'column' } });
    
    return (
        <SortableContext id={id} items={tasks.map(t => t.id)} strategy={verticalListSortingStrategy}>
            <div ref={setNodeRef} className={`flex flex-col rounded-lg bg-gray-800/50 shadow-md transition-colors ${isOver ? 'bg-gray-700/50' : ''}`}>
                <div className="flex items-center justify-between p-3 border-b border-gray-700/50">
                    <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-white">{title}</h3>
                        <span className="text-sm text-gray-400 bg-gray-700/80 rounded-full px-2 py-0.5">{tasks.length}</span>
                    </div>
                    <button onClick={onAddTask} className="text-gray-400 hover:text-white">
                        <Plus size={18} />
                    </button>
                </div>
                <div className="flex-grow p-2 space-y-3 min-h-[100px] overflow-y-auto">
                    {tasks.map(task => (
                        <TaskCard key={task.id} task={task} onEditTask={onEditTask} />
                    ))}
                </div>
            </div>
        </SortableContext>
    );
};
