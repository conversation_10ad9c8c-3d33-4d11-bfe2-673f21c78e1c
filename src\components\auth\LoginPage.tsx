import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Plus } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import type { User as UserType } from '../../types';

interface LoginPageProps {
    onAuthenticated: (user: UserType) => void;
}

export const LoginPage: React.FC<LoginPageProps> = ({ onAuthenticated }) => {
    const [identities, setIdentities] = useState<any[]>([]);
    const [password, setPassword] = useState('');
    const [selectedIdentity, setSelectedIdentity] = useState<any>(null);
    const [error, setError] = useState<string>('');
    const { isLoading, loadIdentities, loginWithIdentity } = useIdentityManagement();
    const navigate = useNavigate();

    useEffect(() => {
        const loadedIdentities = loadIdentities();
        setIdentities(loadedIdentities);
    }, [loadIdentities]);

    const handleIdentitySelect = (identity: any) => {
        setSelectedIdentity(identity);
        setError('');
        setPassword('');
    };

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedIdentity || !password) return;

        setError('');
        const result = await loginWithIdentity(selectedIdentity, password);
        
        if (result.success && result.user) {
            onAuthenticated(result.user);
            navigate('/');
        } else {
            setError(result.error || 'Authentication failed. Please check your password.');
        }
    };

    const handleCreateNew = () => {
        navigate('/create-identity');
    };

    const handleBack = () => {
        setSelectedIdentity(null);
        setPassword('');
        setError('');
    };

    return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
            <div className="bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full border border-gray-700">
                <div className="p-6 border-b border-gray-700">
                    <div className="text-center">
                        <h1 className="text-4xl font-bold text-white mb-2">Momentum</h1>
                        <p className="text-indigo-400">Decentralized Identity Management</p>
                    </div>
                </div>

                <div className="p-6 space-y-6">
                    {!selectedIdentity ? (
                        <>
                            {identities.length > 0 && (
                                <div className="bg-gray-800 p-6 rounded-lg">
                                    <h2 className="text-2xl font-semibold mb-4 text-center text-white">Choose Existing Identity</h2>
                                    {isLoading ? (
                                        <div className="text-center py-8">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto"></div>
                                            <p className="mt-2 text-gray-300">Loading...</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            {identities.map((identity, index) => (
                                                <button
                                                    key={index}
                                                    onClick={() => handleIdentitySelect(identity)}
                                                    className="w-full text-left bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                                                >
                                                    <span>{identity.name}</span><br/>
                                                    <span className="text-xs text-gray-400 font-mono">{identity.publicKey.substring(0, 24)}...</span>
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="bg-gray-800 p-6 rounded-lg text-center">
                                <h2 className="text-2xl font-semibold mb-4 text-white">Create New Identity</h2>
                                <p className="text-gray-400 mb-4">Generate a new secure identity to use with Momentum.</p>
                                <button
                                    onClick={handleCreateNew}
                                    className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                                >
                                    <Plus size={20} />
                                    Create New Identity
                                </button>
                            </div>

                            {identities.length === 0 && (
                                <div className="text-center text-gray-400 py-8">
                                    <User size={48} className="mx-auto mb-4 text-gray-500" />
                                    <h3 className="text-xl font-semibold mb-2">No Identities Found</h3>
                                    <p className="mb-4">Create your first identity to get started with Momentum.</p>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className="bg-gray-800 p-6 rounded-lg">
                            <h2 className="text-2xl font-semibold mb-4 text-center text-white">Login as {selectedIdentity.name}</h2>
                            <form onSubmit={handleLogin} className="space-y-4">
                                <div>
                                    <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                                        Password
                                    </label>
                                    <input
                                        type="password"
                                        id="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        className="w-full px-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                        placeholder="Enter your password"
                                        required
                                        autoFocus
                                    />
                                </div>

                                {error && (
                                    <div className="text-red-400 text-sm text-center">
                                        {error}
                                    </div>
                                )}

                                <div className="flex space-x-3">
                                    <button
                                        type="button"
                                        onClick={handleBack}
                                        className="flex-1 bg-gray-700 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                                    >
                                        Back
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-500 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                                    >
                                        {isLoading ? 'Logging in...' : 'Login'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}

                    <div className="text-center text-sm text-gray-400">
                        <p>🔒 Your identity is stored securely on your device</p>
                        <p>🌐 No data is sent to external servers</p>
                    </div>
                </div>
            </div>
        </div>
    );
};
