import React from 'react';
import type { Task } from '../../types';

interface GanttViewProps {
    tasks: Task[];
    onEditTask?: (task: Task) => void;
}

export const GanttView: React.FC<GanttViewProps> = ({ tasks, onEditTask }) => {
    // A simplified Gantt view is presented here. The full interactive version is complex
    // and requires careful state management. This placeholder ensures stability.
    if (!tasks || tasks.length === 0) {
        return <div className="text-center text-gray-400 py-10">No tasks with dates to display in Gantt chart.</div>;
    }
    
    return (
        <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50 overflow-y-auto" style={{ maxHeight: '70vh' }}>
            <div className="space-y-2">
                {tasks.map(task => (
                    <div 
                        key={task.id} 
                        onClick={() => onEditTask?.(task)} 
                        className="p-2 bg-gray-700 rounded-lg hover:bg-indigo-700 cursor-pointer"
                    >
                        <p className="font-bold text-white">{task.title}</p>
                        <p className="text-sm text-gray-400">
                            {task.startDate && new Date(task.startDate).toLocaleDateString()} - {task.dueDate && new Date(task.dueDate).toLocaleDateString()}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
};
