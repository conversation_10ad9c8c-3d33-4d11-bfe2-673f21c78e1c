import React, { useState, useMemo } from 'react';
import type { Task, User } from '../types';
import { useData } from '../hooks';
import { App<PERSON><PERSON><PERSON>, Header } from './layout';
import {
    DashboardView,
    DayPlannerView,
    NoProjectsView,
    BoardView,
    ListView,
    CalendarView,
    GanttView
} from './views';
import { LoadingSpinner, ProjectModal, TaskModal } from './ui';

interface MainAppProps {
    user: User;
    onLogout: () => boolean;
}

export const MainApp: React.FC<MainAppProps> = ({ user, onLogout }) => {
    // Data management
    const {
        projects,
        activeProject,
        setActiveProject,
        tasks,
        allTasks,
        isLoading,
        statuses,
        allMilestones,
        tasksByStatus,
        tasksWithDates,
        handleSaveProject,
        handleSaveTask,
        handleAddComment,
        handleDeleteTask,
        clearUserData
    } = useData(user, true);

    // UI state
    const [showTaskModal, setShowTaskModal] = useState(false);
    const [showProjectModal, setShowProjectModal] = useState(false);
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [currentView, setCurrentView] = useState('Dashboard');
    const [isDrawerOpen, setIsDrawerOpen] = useState(true);
    const [plannerDate, setPlannerDate] = useState(new Date());

    // Computed values
    const dailyTasks = useMemo(() => {
        const dateStr = plannerDate.toISOString().split('T')[0];
        return allTasks.filter(task => 
            task.startDate === dateStr || 
            task.dueDate === dateStr ||
            (task.startDate && task.dueDate && task.startDate <= dateStr && task.dueDate >= dateStr)
        );
    }, [allTasks, plannerDate]);

    // Local handlers
    const handleNewTaskForDate = (date: Date) => {
        if (!activeProject) {
            alert("Please select a project first.");
            return;
        }
        const isoDate = date.toISOString().split('T')[0];
        setSelectedTask({ startDate: isoDate, dueDate: isoDate } as Task);
        setShowTaskModal(true);
    };

    const handleTaskSave = (taskData: Partial<Task>) => {
        handleSaveTask(taskData);
        setShowTaskModal(false);
        setSelectedTask(null);
    };

    const handleProjectSave = (projectName: string) => {
        handleSaveProject(projectName);
        setCurrentView('Board');
        setShowProjectModal(false);
    };

    const handleUserLogout = () => {
        if (window.confirm('Are you sure you want to logout? You will need to re-authenticate to access your data.')) {
            clearUserData();
            setCurrentView('Dashboard');
            return onLogout();
        }
        return false;
    };

    // Render Logic
    const renderView = () => {
        if (!activeProject && projects.length > 0 && !isLoading) {
            setActiveProject(projects[0]);
            return <LoadingSpinner />;
        }

        switch(currentView) {
            case 'Dashboard':
                return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
            case 'Day':
                return <DayPlannerView
                    tasks={dailyTasks}
                    date={plannerDate}
                    setDate={setPlannerDate}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                />;
            case 'Board':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <BoardView
                    tasks={tasks}
                    setTasks={() => {}} // This will be handled by the hook
                    statuses={statuses}
                    tasksByStatus={tasksByStatus}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onDeleteTask={handleDeleteTask}
                    onAddTask={() => handleNewTaskForDate(new Date())}
                    activeProject={activeProject}
                    user={user}
                />;
            case 'List':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <ListView
                    statuses={statuses}
                    tasksByStatus={tasksByStatus}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onDeleteTask={handleDeleteTask}
                />;
            case 'Calendar':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <CalendarView
                    tasks={tasks}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                    onNewTaskForDate={handleNewTaskForDate}
                />;
            case 'Gantt':
                if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />;
                return <GanttView
                    tasks={tasksWithDates}
                    onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }}
                />;
            default:
                return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
        }
    };

    return (
        <>
            <div className="flex h-screen bg-gray-900 text-gray-100 font-sans">
                <AppDrawer
                    isOpen={isDrawerOpen}
                    projects={projects}
                    activeProject={activeProject}
                    setActiveProject={setActiveProject}
                    onNewProject={() => setShowProjectModal(true)}
                    currentView={currentView}
                    setCurrentView={setCurrentView}
                    onLogout={handleUserLogout}
                    user={user}
                />
                <div className="flex-1 flex flex-col overflow-hidden">
                    <Header
                        onAddTask={() => handleNewTaskForDate(new Date())}
                        isDrawerOpen={isDrawerOpen}
                        toggleDrawer={() => setIsDrawerOpen(!isDrawerOpen)}
                        activeProject={activeProject}
                        currentView={currentView}
                        user={user}
                        onLogout={handleUserLogout}
                    />
                    <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
                        {renderView()}
                    </main>
                </div>
            </div>

            {showTaskModal && (
                <TaskModal
                    task={selectedTask}
                    project={activeProject || projects.find(p => p.id === selectedTask?.projectId)}
                    onClose={() => {setShowTaskModal(false); setSelectedTask(null);}}
                    onSave={handleTaskSave}
                    onDelete={handleDeleteTask}
                    onAddComment={handleAddComment}
                    statuses={statuses}
                />
            )}
            {showProjectModal && (
                <ProjectModal
                    onClose={() => setShowProjectModal(false)}
                    onSave={handleProjectSave}
                />
            )}
        </>
    );
};
