import React, { useState } from 'react';

interface ProjectModalProps {
    onClose: () => void;
    onSave: (projectName: string) => void;
}

export const ProjectModal: React.FC<ProjectModalProps> = ({ onClose, onSave }) => {
    const [name, setName] = useState('');
    
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if(name.trim()) {
            onSave(name.trim());
        }
    };
    
    return (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4 backdrop-blur-sm" onClick={onClose}>
            <div className="bg-gray-800 rounded-xl shadow-2xl w-full max-w-md border border-gray-700" onClick={e => e.stopPropagation()}>
                <form onSubmit={handleSubmit}>
                    <div className="p-6">
                        <h2 className="text-xl font-bold mb-4">New Project</h2>
                        <label htmlFor="projectName" className="text-sm font-medium text-gray-300">Project Name</label>
                        <input
                            id="projectName"
                            type="text"
                            value={name}
                            onChange={e => setName(e.target.value)}
                            placeholder="e.g. Website Redesign"
                            className="w-full mt-2 bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            autoFocus
                        />
                    </div>
                    <div className="flex justify-end items-center p-4 bg-gray-800/50 border-t border-gray-700 rounded-b-xl gap-4">
                        <button type="button" onClick={onClose} className="px-4 py-2 rounded-md text-gray-300 hover:bg-gray-700">Cancel</button>
                        <button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500">Create Project</button>
                    </div>
                </form>
            </div>
        </div>
    );
};
