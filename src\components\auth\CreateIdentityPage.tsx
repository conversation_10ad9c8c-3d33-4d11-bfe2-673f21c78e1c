import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Lock, ArrowLeft } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import type { User as UserType } from '../../types';

interface CreateIdentityPageProps {
    onIdentityCreated: (user: UserType) => void;
}

export const CreateIdentityPage: React.FC<CreateIdentityPageProps> = ({ onIdentityCreated }) => {
    const [step, setStep] = useState<'form' | 'entropy' | 'creating'>('form');
    const [name, setName] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [entropyCollected, setEntropyCollected] = useState(0);
    const [entropy] = useState(new Uint8Array(32));
    const [error, setError] = useState('');
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const { createIdentity } = useIdentityManagement();
    const navigate = useNavigate();

    const requiredEntropy = 256;

    useEffect(() => {
        if (step !== 'entropy' || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const fixedWidth = 400;
        const fixedHeight = 128;
        canvas.width = fixedWidth;
        canvas.height = fixedHeight;

        ctx.fillStyle = '#1F2937';
        ctx.fillRect(0, 0, fixedWidth, fixedHeight);

        const handleMouseMove = (e: MouseEvent) => {
            if (entropyCollected >= requiredEntropy) return;

            const rect = canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (fixedWidth / rect.width);
            const y = (e.clientY - rect.top) * (fixedHeight / rect.height);
            const time = Date.now();

            const entropyByte = (x ^ y ^ time) & 0xFF;
            const index = entropyCollected % 32;
            entropy[index] ^= entropyByte;

            setEntropyCollected(prev => Math.min(prev + 1, requiredEntropy));

            ctx.fillStyle = '#4F46E5';
            ctx.fillRect(x - 2, y - 2, 4, 4);
        };

        canvas.addEventListener('mousemove', handleMouseMove);
        return () => canvas.removeEventListener('mousemove', handleMouseMove);
    }, [step, entropyCollected, entropy, requiredEntropy]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        
        if (password !== confirmPassword) {
            setError('Passwords do not match');
            return;
        }
        if (password.length < 8) {
            setError('Password must be at least 8 characters long');
            return;
        }
        setStep('entropy');
    };

    const handleCreateIdentity = async () => {
        if (entropyCollected < requiredEntropy) {
            setError('Please collect more entropy by moving your mouse');
            return;
        }

        setStep('creating');
        setError('');
        
        const result = await createIdentity(name, password, entropy);

        if (result.success && result.identity) {
            const user: UserType = {
                name: result.identity.name,
                publicKey: result.identity.publicKey,
                uid: result.identity.publicKey
            };
            onIdentityCreated(user);
            navigate('/');
        } else {
            setError(result.error || 'Failed to create identity');
            setStep('entropy');
        }
    };

    const handleBack = () => {
        if (step === 'form') {
            navigate('/login');
        } else if (step === 'entropy') {
            setStep('form');
        }
    };

    return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
            <div className="bg-gray-800 rounded-xl shadow-2xl max-w-md w-full border border-gray-700">
                <div className="p-6 border-b border-gray-700">
                    <div className="flex items-center gap-3">
                        <button
                            onClick={handleBack}
                            className="text-gray-400 hover:text-white transition-colors"
                        >
                            <ArrowLeft size={24} />
                        </button>
                        <h2 className="text-xl font-semibold text-white">Create New Identity</h2>
                    </div>
                </div>

                <div className="p-6">
                    {step === 'form' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                                    Identity Name
                                </label>
                                <div className="relative">
                                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="text"
                                        id="name"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                        placeholder="Enter your name"
                                        required
                                        autoFocus
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                                    Password
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="password"
                                        id="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                        placeholder="Enter password"
                                        required
                                        minLength={8}
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
                                    Confirm Password
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <input
                                        type="password"
                                        id="confirmPassword"
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        className="w-full pl-10 pr-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                        placeholder="Confirm password"
                                        required
                                        minLength={8}
                                    />
                                </div>
                            </div>

                            {error && (
                                <div className="text-red-400 text-sm text-center">
                                    {error}
                                </div>
                            )}

                            <button
                                type="submit"
                                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-500 transition-colors"
                            >
                                Continue
                            </button>
                        </form>
                    )}

                    {step === 'entropy' && (
                        <div className="space-y-4">
                            <div>
                                <h3 className="text-lg font-medium text-white mb-2">Entropy Collection</h3>
                                <p className="text-sm text-gray-300 mb-4">
                                    Move your mouse around the area below to generate randomness for your identity.
                                </p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between text-sm text-gray-300">
                                    <span>Progress:</span>
                                    <span>{Math.round((entropyCollected / requiredEntropy) * 100)}%</span>
                                </div>
                                <div className="w-full bg-gray-700 rounded-full h-2">
                                    <div
                                        className="bg-indigo-500 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${(entropyCollected / requiredEntropy) * 100}%` }}
                                    ></div>
                                </div>
                            </div>

                            <canvas
                                ref={canvasRef}
                                className="w-full h-32 bg-gray-800 rounded border-2 border-dashed border-gray-600 cursor-crosshair"
                                style={{ 
                                    touchAction: 'none',
                                    width: '100%',
                                    height: '128px',
                                    maxWidth: '400px',
                                    display: 'block'
                                }}
                            />

                            {error && (
                                <div className="text-red-400 text-sm text-center">
                                    {error}
                                </div>
                            )}

                            <div className="flex space-x-3">
                                <button
                                    onClick={handleBack}
                                    className="flex-1 bg-gray-700 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                                >
                                    Back
                                </button>
                                <button
                                    onClick={handleCreateIdentity}
                                    disabled={entropyCollected < requiredEntropy}
                                    className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-500 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors"
                                >
                                    Create Identity
                                </button>
                            </div>
                        </div>
                    )}

                    {step === 'creating' && (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto mb-4"></div>
                            <p className="text-gray-300 mb-2">Creating your identity...</p>
                            <p className="text-sm text-gray-400">This may take a moment</p>
                            {error && (
                                <div className="text-red-400 text-sm mt-4">
                                    {error}
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div className="p-6 border-t border-gray-700 text-center text-sm text-gray-400">
                    <p>🔒 Your identity is stored securely on your device</p>
                    <p>🌐 No data is sent to external servers</p>
                </div>
            </div>
        </div>
    );
};
