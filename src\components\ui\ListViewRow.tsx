import React from 'react';
import { Star, User, Calendar as CalendarIcon, Diamond, Trash2 } from 'lucide-react';
import type { Task } from '../../types';

interface ListViewRowProps {
    task: Task;
    onEditTask: (task: Task) => void;
    onDeleteTask: (taskId: string) => void;
}

export const ListViewRow: React.FC<ListViewRowProps> = ({ task, onEditTask, onDeleteTask }) => {
    const priorityStyles: Record<string, string> = {
        High: 'bg-red-500/20 text-red-400',
        Medium: 'bg-yellow-500/20 text-yellow-400',
        Low: 'bg-blue-500/20 text-blue-400',
        None: 'bg-gray-500/20 text-gray-400'
    };
    
    return (
        <div className="flex items-center justify-between p-3 hover:bg-gray-700/50 transition-colors">
            <div className="flex items-center gap-4 flex-1 cursor-pointer" onClick={() => onEditTask(task)}>
                <span className="text-gray-400 text-sm">{task.id.substring(0, 6)}</span>
                <p className="text-base text-gray-200">{task.title}</p>
                {task.isMilestone && <Star size={16} className="text-amber-400 ml-2" />}
            </div>
            <div className="flex items-center gap-6">
                <span className={`px-3 py-1 text-xs font-semibold rounded-full ${priorityStyles[task.priority || 'None']}`}>
                    {task.priority || 'None'}
                </span>
                <div className="flex items-center gap-2 text-sm text-gray-400 w-44">
                    {task.dueDate ?
                        <>
                            <CalendarIcon size={16} />
                            <span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                        </> :
                        <span>-</span>
                    }
                </div>
                <div className="flex items-center gap-2 text-sm text-red-400 w-44">
                    {task.deadline ?
                        <>
                            <Diamond size={16} />
                            <span>{new Date(task.deadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                        </> :
                        <span>-</span>
                    }
                </div>
                <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center" title="Assignee">
                    <User size={16} className="text-gray-300" />
                </div>
                <button onClick={() => onDeleteTask(task.id)} className="text-gray-400 hover:text-red-400">
                    <Trash2 size={16} />
                </button>
            </div>
        </div>
    );
};
