import React, { useState, useEffect } from 'react';
import { User, UserPlus, LogIn, X } from 'lucide-react';
import type { StoredIdentity, User as UserType } from '../../types';

interface IdentityMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onIdentitySwitch: (user: UserType) => void;
    onCreateIdentity: () => void;
    currentUser: UserType;
}

export const IdentityMenu: React.FC<IdentityMenuProps> = ({
    isOpen,
    onClose,
    onIdentitySwitch,
    onCreateIdentity,
    currentUser
}) => {
    const [identities, setIdentities] = useState<StoredIdentity[]>([]);
    const [selectedIdentity, setSelectedIdentity] = useState<StoredIdentity | null>(null);
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showPasswordInput, setShowPasswordInput] = useState(false);

    useEffect(() => {
        if (isOpen) {
            loadIdentities();
        }
    }, [isOpen]);

    const loadIdentities = () => {
        const stored = localStorage.getItem('momentum_identities');
        if (stored) {
            const parsedIdentities = JSON.parse(stored);
            setIdentities(parsedIdentities);
        }
    };

    const decryptData = async (encryptedData: any, password: string): Promise<string | null> => {
        try {
            const encoder = new TextEncoder();
            const decoder = new TextDecoder();
            
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: new Uint8Array(atob(encryptedData.salt).split('').map(c => c.charCodeAt(0))),
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-GCM', length: 256 },
                false,
                ['decrypt']
            );
            
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: new Uint8Array(atob(encryptedData.iv).split('').map(c => c.charCodeAt(0)))
                },
                key,
                new Uint8Array(atob(encryptedData.encrypted).split('').map(c => c.charCodeAt(0)))
            );
            
            return decoder.decode(decrypted);
        } catch (error) {
            console.error('Decryption failed:', error);
            return null;
        }
    };

    const handleIdentitySelect = (identity: StoredIdentity) => {
        setSelectedIdentity(identity);
        setShowPasswordInput(true);
        setPassword('');
    };

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedIdentity || !password) return;

        setIsLoading(true);

        try {
            const decryptedSecretKey = await decryptData(selectedIdentity.encryptedSecretKey, password);

            if (decryptedSecretKey) {
                const sessionIdentity = {
                    name: selectedIdentity.name,
                    publicKey: selectedIdentity.publicKey,
                    secretKey: decryptedSecretKey,
                    timestamp: Date.now()
                };
                sessionStorage.setItem('momentum_session', JSON.stringify(sessionIdentity));

                // Call the success callback
                onIdentitySwitch({
                    name: selectedIdentity.name,
                    publicKey: selectedIdentity.publicKey,
                    uid: selectedIdentity.publicKey
                });

                // Reset state and close
                setShowPasswordInput(false);
                setSelectedIdentity(null);
                setPassword('');
                onClose();
            } else {
                alert('Incorrect password. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('Login failed. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleBack = () => {
        setShowPasswordInput(false);
        setSelectedIdentity(null);
        setPassword('');
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[80vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white">Identity Management</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white"
                    >
                        <X size={20} />
                    </button>
                </div>

                {!showPasswordInput ? (
                    <div className="space-y-4">
                        <div className="bg-gray-700 p-4 rounded-lg">
                            <h3 className="text-sm font-medium text-gray-300 mb-2">Current Identity</h3>
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
                                    <User size={20} className="text-white" />
                                </div>
                                <div>
                                    <p className="font-semibold text-white">{currentUser.name}</p>
                                    <p className="text-xs text-gray-400 font-mono">{currentUser.publicKey.substring(0, 24)}...</p>
                                </div>
                            </div>
                        </div>

                        {identities.filter(id => id.publicKey !== currentUser.publicKey).length > 0 && (
                            <div>
                                <h3 className="text-sm font-medium text-gray-300 mb-3">Switch to Another Identity</h3>
                                <div className="space-y-2">
                                    {identities
                                        .filter(identity => identity.publicKey !== currentUser.publicKey)
                                        .map((identity, index) => (
                                            <button
                                                key={index}
                                                onClick={() => handleIdentitySelect(identity)}
                                                className="w-full text-left bg-gray-700 hover:bg-gray-600 text-white p-3 rounded-lg transition-colors flex items-center space-x-3"
                                            >
                                                <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                                    <User size={16} className="text-white" />
                                                </div>
                                                <div>
                                                    <span className="font-medium">{identity.name}</span><br />
                                                    <span className="text-xs text-gray-400 font-mono">{identity.publicKey.substring(0, 24)}...</span>
                                                </div>
                                            </button>
                                        ))}
                                </div>
                            </div>
                        )}

                        <button
                            onClick={() => {
                                onClose();
                                onCreateIdentity();
                            }}
                            className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                        >
                            <UserPlus size={18} />
                            <span>Create New Identity</span>
                        </button>
                    </div>
                ) : (
                    <div>
                        <div className="mb-4">
                            <h3 className="text-lg font-medium text-white mb-2">Login as {selectedIdentity?.name}</h3>
                            <p className="text-sm text-gray-400 font-mono">{selectedIdentity?.publicKey.substring(0, 24)}...</p>
                        </div>

                        <form onSubmit={handleLogin}>
                            <div className="mb-4">
                                <label htmlFor="password" className="block text-gray-300 text-sm font-medium mb-2">
                                    Password
                                </label>
                                <input
                                    type="password"
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required
                                    disabled={isLoading}
                                    autoFocus
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <button
                                    type="button"
                                    onClick={handleBack}
                                    className="text-gray-400 hover:text-white"
                                    disabled={isLoading}
                                >
                                    Back
                                </button>
                                <button
                                    type="submit"
                                    className="bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:bg-gray-600 flex items-center space-x-2"
                                    disabled={isLoading}
                                >
                                    <LogIn size={16} />
                                    <span>{isLoading ? 'Logging in...' : 'Login'}</span>
                                </button>
                            </div>
                        </form>
                    </div>
                )}
            </div>
        </div>
    );
};
