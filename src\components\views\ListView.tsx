import React from 'react';
import type { ListViewProps } from '../../types';
import { ListViewRow } from '../ui/ListViewRow';

export const ListView: React.FC<ListViewProps> = ({ statuses, tasksByStatus, onEditTask, onDeleteTask }) => (
    <div className="space-y-8">
        {statuses.map(status => {
            const tasksInStatus = tasksByStatus[status] || [];
            if (tasksInStatus.length === 0) return null;
            return (
                <div key={status}>
                    <div className="flex items-center space-x-3 mb-4">
                        <h2 className="text-xl font-semibold text-white">{status}</h2>
                        <span className="text-sm text-gray-400 bg-gray-700/80 rounded-full px-2.5 py-1">{tasksInStatus.length}</span>
                    </div>
                    <div className="bg-gray-800/50 rounded-lg border border-gray-700/50">
                        <div className="divide-y divide-gray-700/50">
                            {tasksInStatus.map(task => (
                                <ListViewRow 
                                    key={task.id} 
                                    task={task} 
                                    onEditTask={onEditTask} 
                                    onDeleteTask={onDeleteTask}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            );
        })}
    </div>
);
