import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, User, Plus } from 'lucide-react';
import { useIdentityManagement } from '../../hooks/useIdentityManagement';
import { IdentityModal } from './IdentityModal';
import type { User as UserType } from '../../types';

interface AuthModalProps {
    isOpen: boolean;
    onClose: () => void;
    onAuthenticated: (user: UserType) => void;
}

export const AuthModal: React.FC<AuthModalProps> = ({
    isOpen,
    onClose,
    onAuthenticated
}) => {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [identities, setIdentities] = useState<any[]>([]);
    const { isLoading, loadIdentities, loginWithIdentity } = useIdentityManagement();

    useEffect(() => {
        if (isOpen) {
            const loadedIdentities = loadIdentities();
            setIdentities(loadedIdentities);
        }
    }, [isOpen, loadIdentities]);

    const handleIdentityCreated = (user: UserType) => {
        setShowCreateModal(false);
        const loadedIdentities = loadIdentities();
        setIdentities(loadedIdentities);
        onAuthenticated(user);
        onClose();
    };

    const handleIdentitySelect = async (identity: any) => {
        const password = prompt(`Enter password for ${identity.name}:`);
        if (!password) return;

        const result = await loginWithIdentity(identity, password);
        
        if (result.success && result.user) {
            onAuthenticated(result.user);
            onClose();
        } else {
            alert(result.error || 'Authentication failed. Please check your password.');
        }
    };

    if (!isOpen) return null;

    const modalContent = (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full mx-4 border border-gray-700">
                <div className="flex items-center justify-between p-6 border-b border-gray-700">
                    <div className="text-center flex-1">
                        <h1 className="text-4xl font-bold text-white mb-2">Momentum</h1>
                        <p className="text-indigo-400">Decentralized Identity Management</p>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white transition-colors"
                    >
                        <X size={24} />
                    </button>
                </div>

                <div className="p-6 space-y-6">
                    {identities.length > 0 && (
                        <div className="bg-gray-800 p-6 rounded-lg">
                            <h2 className="text-2xl font-semibold mb-4 text-center text-white">Choose Existing Identity</h2>
                            {isLoading ? (
                                <div className="text-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto"></div>
                                    <p className="mt-2 text-gray-300">Loading...</p>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {identities.map((identity, index) => (
                                        <button
                                            key={index}
                                            onClick={() => handleIdentitySelect(identity)}
                                            className="w-full text-left bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                                        >
                                            <span>{identity.name}</span><br/>
                                            <span className="text-xs text-gray-400 font-mono">{identity.publicKey.substring(0, 24)}...</span>
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    <div className="bg-gray-800 p-6 rounded-lg text-center">
                        <h2 className="text-2xl font-semibold mb-4 text-white">Create New Identity</h2>
                        <p className="text-gray-400 mb-4">Generate a new secure identity to use with Momentum.</p>
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                        >
                            Create New Identity
                        </button>
                    </div>
                </div>
            </div>

            {showCreateModal && (
                <IdentityModal
                    isOpen={showCreateModal}
                    onClose={() => setShowCreateModal(false)}
                    onIdentityCreated={handleIdentityCreated}
                />
            )}
        </div>
    );

    return createPortal(modalContent, document.body);
};
