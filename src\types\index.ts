// Core application types

export interface User {
    name: string;
    publicKey: string;
    uid: string;
}

export interface StoredIdentity {
    name: string;
    publicKey: string;
    encryptedSecretKey: {
        encrypted: string;
        iv: string;
        salt: string;
    };
}

export interface Project {
    id: string;
    name: string;
    createdAt: string;
}

export interface Task {
    id: string;
    projectId: string;
    title: string;
    description?: string;
    status: string;
    priority?: string;
    startDate?: string;
    dueDate?: string;
    deadline?: string;
    isMilestone?: boolean;
    order: number;
    createdAt: string;
    activity?: ActivityItem[];
}

export interface ActivityItem {
    id: string;
    type: 'comment' | 'history';
    text?: string;
    authorId?: string;
    authorName?: string;
    timestamp: string;
    changedBy?: string;
    changes?: Array<{
        field: string;
        oldValue: string;
        newValue: string;
    }>;
}

// Extended types for views
export interface TaskWithProject extends Task {
    projectName?: string;
}

// Component prop types
export interface AppDrawerProps {
    isOpen: boolean;
    projects: Project[];
    activeProject: Project | null;
    setActiveProject: (project: Project | null) => void;
    onNewProject: () => void;
    currentView: string;
    setCurrentView: (view: string) => void;
    onLogout: () => boolean;
    user: User;
}

export interface HeaderProps {
    onAddTask: () => void;
    isDrawerOpen: boolean;
    toggleDrawer: () => void;
    activeProject: Project | null;
    currentView: string;
    user: User;
    onLogout: () => boolean;
}

export interface LoginComponentProps {
    authError?: string | null;
    onLoginSuccess: (user: User) => void;
}

export interface DashboardViewProps {
    project: Project | null;
    tasks: Task[];
    allMilestones: TaskWithProject[];
}

export interface DayPlannerViewProps {
    tasks: TaskWithProject[];
    date: Date;
    setDate: React.Dispatch<React.SetStateAction<Date>>;
    onEditTask: (task: Task) => void;
}

export interface NoProjectsViewProps {
    onNewProject: () => void;
}

export interface BoardViewProps {
    tasks: Task[];
    setTasks: (tasks: Task[]) => void;
    statuses: string[];
    tasksByStatus: Record<string, Task[]>;
    onEditTask: (task: Task) => void;
    onDeleteTask: (taskId: string) => void;
    onAddTask: () => void;
    activeProject: Project;
    user: User;
}

export interface ListViewProps {
    statuses: string[];
    tasksByStatus: Record<string, Task[]>;
    onEditTask: (task: Task) => void;
    onDeleteTask: (taskId: string) => void;
}

export interface CalendarViewProps {
    tasks: Task[];
    onEditTask: (task: Task) => void;
    onNewTaskForDate: (date: Date) => void;
}
