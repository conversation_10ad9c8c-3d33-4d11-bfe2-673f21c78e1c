import React from 'react';
import { Repeat } from 'lucide-react';
import type { ActivityItem } from '../../types';

interface ActivityItemProps {
    item: ActivityItem;
}

export const ActivityItemComponent: React.FC<ActivityItemProps> = ({ item }) => {
    const formatActivityDate = (timestamp: string) => {
        if (!timestamp) return 'Just now';
        return new Date(timestamp).toLocaleString();
    };

    if (item.type === 'comment') {
        return (
            <div className="bg-gray-700/50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-300">{item.authorName}</span>
                    <span className="text-xs text-gray-500">{formatActivityDate(item.timestamp)}</span>
                </div>
                <p className="text-sm text-gray-200">{item.text}</p>
            </div>
        );
    }

    if (item.type === 'history') {
        return (
            <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center mt-0.5">
                    <Repeat size={12} className="text-gray-300" />
                </div>
                <div className="flex-1">
                    <div className="text-xs text-gray-500 mb-1">{formatActivityDate(item.timestamp)}</div>
                    {item.changes?.map((change, index) => (
                        <div key={index} className="text-sm text-gray-300 mb-1">
                            <span className="font-medium">{change.field}</span> changed from{' '}
                            <span className="text-red-400">{change.oldValue}</span> to{' '}
                            <span className="text-green-400">{change.newValue}</span>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return null;
};
