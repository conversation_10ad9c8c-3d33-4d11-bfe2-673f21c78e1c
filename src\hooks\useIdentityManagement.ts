import { useState, useCallback } from 'react';
import type { StoredIdentity, User } from '../types';

export const useIdentityManagement = () => {
    const [isLoading, setIsLoading] = useState(false);

    const encryptData = async (data: string, password: string) => {
        const encoder = new TextEncoder();
        const decoder = new TextDecoder();
        
        // Generate salt and IV
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(12));
        
        // Derive key from password
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            encoder.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );
        
        const key = await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 100000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: 'AES-GCM', length: 256 },
            false,
            ['encrypt']
        );
        
        // Encrypt the data
        const encrypted = await crypto.subtle.encrypt(
            {
                name: 'AES-GCM',
                iv: iv
            },
            key,
            encoder.encode(data)
        );
        
        return {
            encrypted: btoa(String.fromCharCode(...new Uint8Array(encrypted))),
            iv: btoa(String.fromCharCode(...iv)),
            salt: btoa(String.fromCharCode(...salt))
        };
    };

    const decryptData = async (encryptedData: any, password: string): Promise<string | null> => {
        try {
            const encoder = new TextEncoder();
            const decoder = new TextDecoder();
            
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                encoder.encode(password),
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );
            
            const key = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: new Uint8Array(atob(encryptedData.salt).split('').map(c => c.charCodeAt(0))),
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-GCM', length: 256 },
                false,
                ['decrypt']
            );
            
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: new Uint8Array(atob(encryptedData.iv).split('').map(c => c.charCodeAt(0)))
                },
                key,
                new Uint8Array(atob(encryptedData.encrypted).split('').map(c => c.charCodeAt(0)))
            );
            
            return decoder.decode(decrypted);
        } catch (error) {
            console.error('Decryption failed:', error);
            return null;
        }
    };

    const loadIdentities = useCallback((): StoredIdentity[] => {
        const stored = localStorage.getItem('momentum_identities');
        return stored ? JSON.parse(stored) : [];
    }, []);

    const saveIdentity = (identity: StoredIdentity) => {
        const identities = loadIdentities();
        const updatedIdentities = [...identities, identity];
        localStorage.setItem('momentum_identities', JSON.stringify(updatedIdentities));
        return updatedIdentities;
    };

    const createIdentity = async (
        name: string, 
        password: string, 
        entropy: Uint8Array
    ): Promise<{ success: boolean; identity?: StoredIdentity; error?: string }> => {
        setIsLoading(true);

        try {
            // Generate keypair using collected entropy
            const keyPair = crypto.getRandomValues(new Uint8Array(32));
            for (let i = 0; i < 32; i++) {
                keyPair[i] ^= entropy[i];
            }

            const publicKey = btoa(String.fromCharCode(...keyPair.slice(0, 16)));
            const secretKey = btoa(String.fromCharCode(...keyPair));

            const encryptedSecretKey = await encryptData(secretKey, password);

            const newIdentity: StoredIdentity = {
                name,
                publicKey,
                encryptedSecretKey
            };

            saveIdentity(newIdentity);

            // Create backup download
            const backupData = { name, publicKey, encryptedSecretKey };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(backupData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `${name}_momentum_identity.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            return { success: true, identity: newIdentity };
        } catch (error) {
            console.error('Error creating identity:', error);
            return { success: false, error: 'Failed to create identity. Please try again.' };
        } finally {
            setIsLoading(false);
        }
    };

    const loginWithIdentity = async (
        identity: StoredIdentity,
        password: string
    ): Promise<{ success: boolean; user?: User; error?: string }> => {
        setIsLoading(true);

        try {
            const decryptedSecretKey = await decryptData(identity.encryptedSecretKey, password);

            if (decryptedSecretKey) {
                const sessionIdentity = {
                    name: identity.name,
                    publicKey: identity.publicKey,
                    secretKey: decryptedSecretKey,
                    timestamp: Date.now()
                };
                sessionStorage.setItem('momentum_session', JSON.stringify(sessionIdentity));

                const user: User = {
                    name: identity.name,
                    publicKey: identity.publicKey,
                    uid: identity.publicKey
                };

                return { success: true, user };
            } else {
                return { success: false, error: 'Incorrect password. Please try again.' };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, error: 'Login failed. Please try again.' };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        isLoading,
        loadIdentities,
        createIdentity,
        loginWithIdentity,
        encryptData,
        decryptData
    };
};
